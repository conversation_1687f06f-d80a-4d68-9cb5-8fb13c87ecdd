/**
 * Integration test for read message functionality
 * 
 * This test verifies the complete flow of:
 * 1. Creating a chat and sending messages
 * 2. Checking unread counts
 * 3. Marking messages as read
 * 4. Verifying read status updates
 */

import { prisma } from '@/lib/prisma';
import { chatMessageApi } from '@/services/chatService';

// Mock the API service to simulate real API calls
jest.mock('@/services/chatService', () => ({
  chatMessageApi: {
    getMessages: jest.fn(),
    markMessageAsRead: jest.fn(),
    markChatAsRead: jest.fn(),
    getUnreadCount: jest.fn(),
    getAllUnreadCounts: jest.fn(),
    getTotalUnreadCount: jest.fn(),
  },
}));

describe('Read Message Integration Tests', () => {
  const mockUser1 = { id: 1, firstName: 'John', lastName: 'Doe' };
  const mockUser2 = { id: 2, firstName: 'Jane', lastName: '<PERSON>' };
  const mockChat = { id: 1, name: 'Test Chat', chatType: 'PRIVATE' };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Message Read Status Flow', () => {
    it('should track read status correctly for new messages', async () => {
      // Mock initial state: chat with unread messages
      const mockMessages = [
        {
          id: 1,
          userId: 2,
          content: 'Hello!',
          createdAt: new Date().toISOString(),
          messageType: 'TEXT',
          messageStatus: 'DELIVERED',
          isRead: false,
          readAt: null,
          user: mockUser2,
        },
        {
          id: 2,
          userId: 2,
          content: 'How are you?',
          createdAt: new Date().toISOString(),
          messageType: 'TEXT',
          messageStatus: 'DELIVERED',
          isRead: false,
          readAt: null,
          user: mockUser2,
        },
      ];

      (chatMessageApi.getMessages as jest.Mock).mockResolvedValue({
        messages: mockMessages,
        pagination: { page: 1, limit: 50, totalCount: 2, totalPages: 1, hasMore: false },
      });

      (chatMessageApi.getUnreadCount as jest.Mock).mockResolvedValue({
        chatId: 1,
        unreadCount: 2,
      });

      // Verify initial unread count
      const unreadCount = await chatMessageApi.getUnreadCount(1);
      expect(unreadCount.unreadCount).toBe(2);

      // Verify messages are marked as unread
      const messages = await chatMessageApi.getMessages(1);
      expect(messages.messages.every((msg: any) => !msg.isRead)).toBe(true);
    });

    it('should mark single message as read', async () => {
      (chatMessageApi.markMessageAsRead as jest.Mock).mockResolvedValue({
        message: 'Message marked as read',
        messageRead: {
          id: 1,
          messageId: 1,
          userId: 1,
          readAt: new Date().toISOString(),
        },
      });

      const result = await chatMessageApi.markMessageAsRead(1);
      expect(result.message).toBe('Message marked as read');
      expect(result.messageRead.messageId).toBe(1);
      expect(result.messageRead.userId).toBe(1);
    });

    it('should mark all messages in chat as read', async () => {
      (chatMessageApi.markChatAsRead as jest.Mock).mockResolvedValue({
        message: 'Messages marked as read',
        markedCount: 2,
        totalMessages: 2,
      });

      const result = await chatMessageApi.markChatAsRead(1);
      expect(result.message).toBe('Messages marked as read');
      expect(result.markedCount).toBe(2);
      expect(result.totalMessages).toBe(2);
    });

    it('should update unread count after marking messages as read', async () => {
      // Initial unread count
      (chatMessageApi.getUnreadCount as jest.Mock).mockResolvedValueOnce({
        chatId: 1,
        unreadCount: 2,
      });

      // Mark messages as read
      (chatMessageApi.markChatAsRead as jest.Mock).mockResolvedValue({
        message: 'Messages marked as read',
        markedCount: 2,
        totalMessages: 2,
      });

      // Updated unread count
      (chatMessageApi.getUnreadCount as jest.Mock).mockResolvedValueOnce({
        chatId: 1,
        unreadCount: 0,
      });

      // Verify flow
      const initialCount = await chatMessageApi.getUnreadCount(1);
      expect(initialCount.unreadCount).toBe(2);

      await chatMessageApi.markChatAsRead(1);

      const updatedCount = await chatMessageApi.getUnreadCount(1);
      expect(updatedCount.unreadCount).toBe(0);
    });

    it('should get unread counts for all chats', async () => {
      const mockUnreadCounts = [
        { chatId: 1, chatName: 'Chat 1', chatType: 'PRIVATE', unreadCount: 3 },
        { chatId: 2, chatName: 'Chat 2', chatType: 'TASK', unreadCount: 0 },
        { chatId: 3, chatName: 'Chat 3', chatType: 'DEPARTMENT', unreadCount: 5 },
      ];

      (chatMessageApi.getAllUnreadCounts as jest.Mock).mockResolvedValue({
        unreadCounts: mockUnreadCounts,
      });

      const result = await chatMessageApi.getAllUnreadCounts();
      expect(result.unreadCounts).toHaveLength(3);
      expect(result.unreadCounts[0].unreadCount).toBe(3);
      expect(result.unreadCounts[2].unreadCount).toBe(5);
    });

    it('should get total unread count across all chats', async () => {
      (chatMessageApi.getTotalUnreadCount as jest.Mock).mockResolvedValue({
        totalUnreadCount: 8,
      });

      const result = await chatMessageApi.getTotalUnreadCount();
      expect(result.totalUnreadCount).toBe(8);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty chat with no messages', async () => {
      (chatMessageApi.getMessages as jest.Mock).mockResolvedValue({
        messages: [],
        pagination: { page: 1, limit: 50, totalCount: 0, totalPages: 0, hasMore: false },
      });

      (chatMessageApi.getUnreadCount as jest.Mock).mockResolvedValue({
        chatId: 1,
        unreadCount: 0,
      });

      const messages = await chatMessageApi.getMessages(1);
      const unreadCount = await chatMessageApi.getUnreadCount(1);

      expect(messages.messages).toHaveLength(0);
      expect(unreadCount.unreadCount).toBe(0);
    });

    it('should handle marking already read messages', async () => {
      (chatMessageApi.markChatAsRead as jest.Mock).mockResolvedValue({
        message: 'No messages to mark as read',
        markedCount: 0,
      });

      const result = await chatMessageApi.markChatAsRead(1);
      expect(result.message).toBe('No messages to mark as read');
      expect(result.markedCount).toBe(0);
    });

    it('should handle user with no chats', async () => {
      (chatMessageApi.getAllUnreadCounts as jest.Mock).mockResolvedValue({
        unreadCounts: [],
      });

      (chatMessageApi.getTotalUnreadCount as jest.Mock).mockResolvedValue({
        totalUnreadCount: 0,
      });

      const allCounts = await chatMessageApi.getAllUnreadCounts();
      const totalCount = await chatMessageApi.getTotalUnreadCount();

      expect(allCounts.unreadCounts).toHaveLength(0);
      expect(totalCount.totalUnreadCount).toBe(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      (chatMessageApi.markMessageAsRead as jest.Mock).mockRejectedValue(
        new Error('Message not found')
      );

      await expect(chatMessageApi.markMessageAsRead(999)).rejects.toThrow('Message not found');
    });

    it('should handle network errors', async () => {
      (chatMessageApi.getUnreadCount as jest.Mock).mockRejectedValue(
        new Error('Network error')
      );

      await expect(chatMessageApi.getUnreadCount(1)).rejects.toThrow('Network error');
    });

    it('should handle unauthorized access', async () => {
      (chatMessageApi.markChatAsRead as jest.Mock).mockRejectedValue(
        new Error('You are not a participant in this chat')
      );

      await expect(chatMessageApi.markChatAsRead(1)).rejects.toThrow(
        'You are not a participant in this chat'
      );
    });
  });
});
